//
//  RateAppFeatureTests.swift
//  ztt2Tests
//
//  Created by AI Assistant on 2025/8/4.
//

import XCTest
import SwiftUI
import StoreKit
@testable import ztt2

/**
 * 应用评分功能测试
 * 测试好评鼓励功能的各个方面
 */
class RateAppFeatureTests: XCTestCase {
    
    override func setUpWithError() throws {
        // 在每个测试前重置UserDefaults
        UserDefaults.standard.removeObject(forKey: "user_has_rated_app")
        UserDefaults.standard.removeObject(forKey: "last_rating_request_date")
    }
    
    override func tearDownWithError() throws {
        // 清理测试数据
        UserDefaults.standard.removeObject(forKey: "user_has_rated_app")
        UserDefaults.standard.removeObject(forKey: "last_rating_request_date")
    }
    
    /**
     * 测试SettingType枚举是否包含rateApp选项
     */
    func testSettingTypeContainsRateApp() throws {
        let allCases = SettingType.allCases
        XCTAssertTrue(allCases.contains(.rateApp), "SettingType应该包含rateApp选项")
        
        // 验证顺序：产品介绍 -> 好评鼓励 -> 帮助与反馈 -> 关于 -> 清除所有数据
        let expectedOrder: [SettingType] = [.productIntroduction, .rateApp, .feedback, .about, .clearAllData]
        XCTAssertEqual(allCases, expectedOrder, "设置项顺序应该正确")
    }
    
    /**
     * 测试rateApp的显示名称
     */
    func testRateAppDisplayName() throws {
        let rateApp = SettingType.rateApp
        XCTAssertEqual(rateApp.displayName, "好评鼓励", "rateApp的显示名称应该是'好评鼓励'")
    }
    
    /**
     * 测试rateApp的图标
     */
    func testRateAppIcon() throws {
        let rateApp = SettingType.rateApp
        XCTAssertEqual(rateApp.iconName, "star.fill", "rateApp应该使用star.fill图标")
        XCTAssertTrue(rateApp.isSystemIcon, "rateApp应该使用系统图标")
    }
    
    /**
     * 测试rateApp的其他属性
     */
    func testRateAppProperties() throws {
        let rateApp = SettingType.rateApp
        XCTAssertFalse(rateApp.isDestructive, "rateApp不应该是破坏性操作")
        XCTAssertFalse(rateApp.hasToggle, "rateApp不应该有开关")
        XCTAssertFalse(rateApp.requiresPaidSubscription, "rateApp不应该需要付费订阅")
    }
    
    /**
     * 测试ProfileView是否能正确创建
     */
    func testProfileViewCreation() throws {
        let profileView = ProfileView()
        XCTAssertNotNil(profileView, "ProfileView应该能够正确创建")
    }
    
    /**
     * 测试用户评分偏好设置
     */
    func testUserRatingPreferences() throws {
        // 初始状态
        XCTAssertFalse(UserDefaults.standard.bool(forKey: "user_has_rated_app"), "初始状态用户应该未评分")
        
        // 设置已评分
        UserDefaults.standard.set(true, forKey: "user_has_rated_app")
        UserDefaults.standard.set(Date(), forKey: "last_rating_request_date")
        
        XCTAssertTrue(UserDefaults.standard.bool(forKey: "user_has_rated_app"), "设置后用户应该已评分")
        XCTAssertNotNil(UserDefaults.standard.object(forKey: "last_rating_request_date"), "应该记录最后请求日期")
    }
    
    /**
     * 测试本地化字符串
     */
    func testLocalizationStrings() throws {
        // 测试设置项名称
        XCTAssertEqual("settings.item.rate_app".localized, "好评鼓励")
        
        // 测试评分弹窗相关字符串
        XCTAssertEqual("rate_app.title".localized, "好评鼓励")
        XCTAssertEqual("rate_app.rate_now".localized, "去评分")
        XCTAssertEqual("rate_app.later".localized, "稍后提醒")
        XCTAssertEqual("rate_app.no_thanks".localized, "不了，谢谢")
        XCTAssertFalse("rate_app.message".localized.isEmpty, "评分消息不应该为空")
    }
    
    /**
     * 测试StoreKit可用性
     */
    func testStoreKitAvailability() throws {
        // 验证SKStoreReviewController类是否可用
        XCTAssertNotNil(SKStoreReviewController.self, "SKStoreReviewController应该可用")
        
        // 在iOS 15.6+上应该支持
        if #available(iOS 14.0, *) {
            // 这个测试主要是确保编译时没有问题
            XCTAssertTrue(true, "iOS 14.0+应该支持SKStoreReviewController")
        }
    }
    
    /**
     * 测试SystemSettingsSection组件
     */
    func testSystemSettingsSection() throws {
        var settingPressed: SettingType?
        
        let settingsSection = SystemSettingsSection { settingType in
            settingPressed = settingType
        }
        
        XCTAssertNotNil(settingsSection, "SystemSettingsSection应该能够正确创建")
        
        // 模拟点击rateApp设置项
        // 注意：这里只是测试组件创建，实际的点击测试需要UI测试
    }
}

// MARK: - 测试辅助扩展

extension String {
    /// 简化的本地化方法，用于测试
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
}
